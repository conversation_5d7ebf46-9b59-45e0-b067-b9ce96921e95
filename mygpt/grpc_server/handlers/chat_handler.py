import time
from typing import List

import asyncio
import uuid
import grpc
from langchain_core.messages import convert_to_messages
from google.protobuf.json_format import MessageToDict
from mygpt.agent_functioncall.callback_handler import (
    FCAgentStream<PERSON>all<PERSON><PERSON><PERSON><PERSON>,
    FCAgentBaseCallbackHandler,
)
from mygpt.agent_functioncall.functioncall_agent_engine import AgentFunction<PERSON>allEngine
from mygpt.agent_functioncall.knowledge_management.dataset_builder import DatasetBaseBuilder
from mygpt.agent_functioncall.schemas.dataset_schemas import BuildDatasetIn, BuildDocumentIn, SourceType
from mygpt.agent_functioncall.schemas.prompt_schemas import PromptType
from mygpt.agent_functioncall.knowledge_management.knowledge_service import (
    KnowledgeService,
)
from mygpt.authorization import verify_robot
from mygpt.endpoints.questions import (
    save_message_question,
    save_session_title,
    save_message_with_status_check,
)
from mygpt.enums import StreamingMessageDataFormat, OpenAIModel
from mygpt.grpc_server.protos.v2 import chat_service_pb2_grpc, chat_service_pb2

from loguru import logger as logging

from mygpt.grpc_server.utils.proto_utils import parse_parameters
from mygpt.openai_utils import get_chat_history_turbo
from mygpt.opensearch_knowledge import OpenSearchKnowledgeClient
from mygpt.dao.opensearch_dao.knowledge_docs_dao import KnowledgeDocsDao
from mygpt.schemata import QuestionIn
from mygpt.service.agent_service import agent_service
from mygpt.dao.postgresql_dao.user_dao import user_dao


class ChatHandler(chat_service_pb2_grpc.ChatServiceServicer):
    def legalize_messages(self, messages):
        # history, count = await get_chat_history_turbo(session_id, 20)
        if messages and messages[0].get("role") == "system":
            system_message = messages.pop(0)
            logging.info(f"【ChatAgentStream】remove system message: {system_message}")
        if not messages:
            logging.error(f"【ChatAgentStream】The messages cannot be empty")
            raise ValueError("The messages cannot be empty")
        if messages[-1].get("role") != "user":
            # 容错处理, 如果最后一条消息不是用户消息且内容为空, 则删除最后一条消息
            logging.warning(
                f"【ChatHandler.legalize_messages】The last message is not a user message, and the content is empty, so delete the last message"
            )
            if messages[-1].get("content").strip() == "":
                messages.pop()
        if messages[-1].get("role") != "user":
            logging.error(f"【ChatAgentStream】The last message is not a user message")
            logging.error(f"【ChatAgentStream】messages: {messages}")
            raise ValueError("The last message is not a user message")
            # await context.abort(
            #     grpc.StatusCode.INVALID_ARGUMENT,
            #     "The last message is not a user message",
            # )
        return messages

    async def ChatAgentStream(self, request, context):
        """
        string robot_id = 1;  // 机器人ID 或者 Project ID 需要是一个UUID
        string agent_mode = 2;  // 机器人模式 [chat, tts, ...]
        repeated ChatMessage messages = 3;   // 消息列表
        map<string, string> parameters = 4;
        """
        logging.info(f"ChatAgentStream: {request}")
        start_time = time.time()
        api_key = request.api_key
        agent_name = request.agent_name
        # session_id = request.session_id
        agent_mode = request.agent_mode
        messages = request.messages
        proto_parameters = request.parameters
        parameters = parse_parameters(proto_parameters)
        # 多层召回还是单层召回
        if agent_mode == "contextual":
            agent_mode = PromptType.CONTEXTUAL_EXP
            service_method = agent_service.chat_agent_for_project
        elif agent_mode == "doctree":
            agent_mode = PromptType.DOCTREE
            service_method = agent_service.chat_agent_for_project_doc_tree_with_multi_level_recall
        else:
            logging.error(f"【ChatAgentStream】agent_mode is not supported: {agent_mode}")
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details("agent_mode is not supported")
            raise ValueError("agent_mode is not supported")
        session_id = str(uuid.uuid4())

        # 使用api_key获取user
        user = await user_dao.find_user_by_apikey(api_key)
        if not user:
            logging.error(f"[ChatAgentStream] api_key is an invalid key")
            context.set_code(grpc.StatusCode.UNAUTHENTICATED)
            context.set_details("Invalid api_key")
            raise ValueError("Invalid api_key")

        model = parameters.get("model", "claude_37_sonnet")
        # 获取articles
        articles = parameters.get("articles", [])
        # 获取project的相关信息
        projects = parameters.get("projects", [])

        # 暂时默认使用claude_37_sonnet
        # base_model = OpenAIModel.CLAUDE_35_SONNET
        base_model = OpenAIModel.CLAUDE_37_SONNET
        # base_model = OpenAIModel.GEMINI_20_FLASH
        # base_model = OpenAIModel.GEMINI_20_PRO_EXP_0205

        # 将grpc的messages转换成python的dict
        messages = [MessageToDict(message) for message in messages]
        try:
            messages = self.legalize_messages(messages)
        except Exception as e:
            logging.error(f"【ChatAgentStreamWithTraditionalRAG】Error: {e}")
            await context.abort(
                grpc.StatusCode.INVALID_ARGUMENT,
                f"【ChatAgentStreamWithTraditionalRAG】Error: {e}",
            )
        # 整理历史消极, 提取最新的用户消息
        question = messages.pop(-1).get("content")
        logging.info(f"【ChatAgentStream】question: {question}")
        max_tokens = int(parameters.get("max_tokens", 0))
        temperature = float(parameters.get("temperature", 0))
        request_timeout = int(parameters.get("request_timeout", 30))
        logging.info(
            f"【ChatAgentStream】model: {model}, max_tokens: {max_tokens}, temperature: {temperature}, request_timeout: {request_timeout}"
        )
        try:
            # 启动异步线程待等event，用于apm统计接收到第1个字符或完整消息的时间，由stream字段决定 (沿用question接口的逻辑, 备用, 暂时未使用)
            event = asyncio.Event()
            finish_event = asyncio.Event()
            # 调用Agent回答问题
            # history, count = await get_chat_history_turbo(question_in.session_id, 12)
            # messages是List[Dict],标准的OpenAI Style, 但是需要转换成langchain的AIMessage对象
            # langchain_messages = messages_from_dict(messages)
            langchain_messages = convert_to_messages(messages)
            logging.info(f"【ChatAgentStream】langchain_messages: {langchain_messages}")
            # 根据base_model来设定max_tokens
            if (
                base_model == OpenAIModel.O3_MINI
                or base_model == OpenAIModel.CLAUDE_37_SONNET
            ):
                max_tokens = 65536
            elif base_model == OpenAIModel.GPT_4_OMNI_2024_11_20:
                max_tokens = 4096
            else:
                max_tokens = 8192
            logging.info(f"【ChatAgentStream】use base_model: {base_model} - max_tokens: {max_tokens}")
            response = await service_method(
                history=langchain_messages,
                question=question,
                session_id=session_id,
                agent_name=agent_name,
                user=user,
                base_model=base_model,
                agent_mode=agent_mode,
                max_tokens=max_tokens,
                event=event,
                articles=articles,
                projects=projects,
            )
            logging.info(f"【ChatAgentStream】response: {response}")
            question_record_obj = response.question_record_obj
            resp = response.gpt_response_iter
            async for chunk in resp:
                if not chunk:
                    break
                logging.info(f"【ChatAgentStream】chunk: {chunk}")
                # ele = stream_callback.queue.get_nowait()
                # logging.info(f"【ChatAgentStream】ele: {ele}")
                yield chat_service_pb2.ChatAgentStreamResponse(chunk=chunk)
        except Exception as e:
            logging.error(f"【ChatAgentStream】Error: {e}")
            import traceback
            logging.warning(f"【ChatAgentStream】traceback: {traceback.format_exc()}")
            await context.abort(grpc.StatusCode.INTERNAL, str(e))
        finally:
            async def finally_operation():
                # 保证最终操作一定会执行
                try:
                    await asyncio.wait_for(finish_event.wait(), timeout=600)
                except asyncio.TimeoutError:
                    logging.error("[finally_operation] TimeoutError")
                    logging.info(f"finish_event timeout - question: {question}")
                asyncio.create_task(save_session_title(session_id, question_record_obj))
                # asyncio.create_task(incr_user_and_robot_question_usage_count(ai_obj))
                success = await save_message_with_status_check(question_record_obj)
                if not success:
                    logging.warning(f"Failed to save message {question_record_obj.id}")
                interval_seconds = time.time() - start_time
                logging.info(
                    f"Stream task create after send request: {interval_seconds} seconds"
                )
            asyncio.create_task(finally_operation())

    async def ChatAgentStreamWithTraditionalRAG(self, request, context):
        logging.info(f"ChatAgentStreamWithTraditionalRAG: {request}")
        start_time = time.time()
        api_key = request.api_key
        agent_name = request.agent_name
        session_id = request.session_id
        # session_id = f"ChatAgentStream_gprc_traditional_rag"
        agent_mode = request.agent_mode
        messages = request.messages
        proto_parameters = request.parameters
        parameters = parse_parameters(proto_parameters)
        agent_mode = PromptType.CUSTOM

        # session_id = request.session_id or "ChatAgentStreamWithTraditionalRAG"
        session_id = str(uuid.uuid4())

        # 暂时默认使用claude_37_sonnet
        base_model = OpenAIModel.CLAUDE_37_SONNET

        # 使用api_key获取user
        user = await user_dao.find_user_by_apikey(api_key)
        if not user:
            logging.error(f"[ChatAgentStream] api_key is an invalid key")
            context.set_code(grpc.StatusCode.UNAUTHENTICATED)
            context.set_details("Invalid api_key")
            raise ValueError("Invalid api_key")

        # 将grpc的messages转换成python的dict
        messages = [MessageToDict(message) for message in messages]
        try:
            messages = self.legalize_messages(messages)
        except Exception as e:
            logging.error(f"【ChatAgentStreamWithTraditionalRAG】Error: {e}")
            await context.abort(
                grpc.StatusCode.INVALID_ARGUMENT,
                f"【ChatAgentStreamWithTraditionalRAG】Error: {e}",
            )
        # 整理历史消极, 提取最新的用户消息
        question = messages.pop(-1).get("content")
        logging.info(f"【ChatAgentStreamWithTraditionalRAG】question: {question}")
        # parameters 参数
        max_tokens = int(parameters.get("max_tokens", 0))
        temperature = float(parameters.get("temperature", 0))
        request_timeout = int(parameters.get("request_timeout", 30))
        dataset_ids = parameters.get("dataset_ids", [])
        enable_tool_call_messages = parameters.get("enable_tool_call_messages", True)
        document_mode = parameters.get("document_mode", False)
        runtime_context = {
            "document_mode": document_mode
        }
        logging.info(
            f"【ChatAgentStreamWithTraditionalRAG】model: {base_model}, max_tokens: {max_tokens}, temperature: {temperature}, request_timeout: {request_timeout}"
        )
        try:
            # 启动异步线程待等event，用于apm统计接收到第1个字符或完整消息的时间，由stream字段决定 (沿用question接口的逻辑, 备用, 暂时未使用)
            event = asyncio.Event()
            finish_event = asyncio.Event()
            # 调用Agent回答问题
            # history, count = await get_chat_history_turbo(question_in.session_id, 12)
            # messages是List[Dict],标准的OpenAI Style, 但是需要转换成langchain的AIMessage对象
            # langchain_messages = messages_from_dict(messages)
            langchain_messages = convert_to_messages(messages)
            logging.info(f"【ChatAgentStreamWithTraditionalRAG】langchain_messages: {langchain_messages}")
            # 根据base_model来设定max_tokens
            if (
                base_model == OpenAIModel.O3_MINI
                or base_model == OpenAIModel.CLAUDE_37_SONNET
            ):
                max_tokens = 32768
            elif base_model == OpenAIModel.GPT_4_OMNI_2024_11_20:
                max_tokens = 4096
            else:
                max_tokens = 8192
            logging.info(f"【ChatAgentStreamWithTraditionalRAG】use base_model: {base_model} - max_tokens: {max_tokens}")
            response = await agent_service.chat_agent_for_project_with_traditional_rag(
                history=langchain_messages,
                question=question,
                user=user,
                session_id=session_id,
                agent_name=agent_name,
                base_model=base_model,
                agent_mode=agent_mode,
                max_tokens=max_tokens,
                dataset_ids=dataset_ids,
                event=event,
                enable_tool_call_messages=enable_tool_call_messages,
                runtime_context=runtime_context
            )
            logging.info(f"【ChatAgentStreamWithTraditionalRAG】response: {response}")
            question_record_obj = response.question_record_obj
            resp = response.gpt_response_iter
            async for chunk in resp:
                if not chunk:
                    break
                logging.info(f"【ChatAgentStreamWithTraditionalRAG】chunk: {chunk}")
                # ele = stream_callback.queue.get_nowait()
                # logging.info(f"【ChatAgentStream】ele: {ele}")
                yield chat_service_pb2.ChatAgentStreamResponse(chunk=chunk)
        except Exception as e:
            logging.error(f"【ChatAgentStreamWithTraditionalRAG】Error: {e}")
            import traceback
            logging.warning(f"【ChatAgentStreamWithTraditionalRAG】traceback: {traceback.format_exc()}")
            await context.abort(grpc.StatusCode.INTERNAL, str(e))
        finally:
            async def finally_operation():
                # 保证最终操作一定会执行
                try:
                    await asyncio.wait_for(finish_event.wait(), timeout=600)
                except asyncio.TimeoutError:
                    logging.error("[finally_operation] TimeoutError")
                    logging.info(f"finish_event timeout - question: {question}")
                asyncio.create_task(save_session_title(session_id, question_record_obj))
                # asyncio.create_task(incr_user_and_robot_question_usage_count(ai_obj))
                success = await save_message_with_status_check(question_record_obj)
                if not success:
                    logging.warning(f"Failed to save message {question_record_obj.id}")
                interval_seconds = time.time() - start_time
                logging.info(
                    f"Stream task create after send request: {interval_seconds} seconds"
                )
            asyncio.create_task(finally_operation())



