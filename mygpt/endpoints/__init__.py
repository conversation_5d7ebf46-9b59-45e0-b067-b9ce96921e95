from fastapi import APIRouter

from mygpt.endpoints.attachments import router as attachments_router
from mygpt.endpoints.dataset import datasets_api_router
from mygpt.endpoints.dataset import robot_router as dataset_robot_router
from mygpt.endpoints.dataset import router as dataset_router
from mygpt.endpoints.debug import router as debug_router
from mygpt.endpoints.digital_human import api_router as api_digital_human_router
from mygpt.endpoints.digital_human import router as digital_human_router
from mygpt.endpoints.embeddings import router as embeddings_router
from mygpt.endpoints.faqs import (
    dataset_faqs_generation_router,
    dataset_faqs_router,
    datasets_property_router,
)
from mygpt.endpoints.faqs import robot_router as faqs_robot_router
from mygpt.endpoints.function_call_api import function_router, agent_function_router
from mygpt.endpoints.insider_preview import router as insider_preview_router

# from mygpt.endpoints.mock_api import mock_router
from mygpt.endpoints.notification import router as report_router
from mygpt.endpoints.questions import api_router as api_questions_router
from mygpt.endpoints.questions import router as questions_router
from mygpt.endpoints.robots import ai_router
from mygpt.endpoints.robots import api_router as api_robot_router
from mygpt.endpoints.robots import router as robot_router
from mygpt.endpoints.session_metrics import router as session_metrics_router
from mygpt.endpoints.stripes import router as stripes_router
from mygpt.endpoints.subscription_add_ons import router as add_ons_router
from mygpt.endpoints.subscription_plans import router as plans_router
from mygpt.endpoints.user_quota import router as user_quota_router
from mygpt.endpoints.users import router as users_router
from mygpt.train.train import train_router

router = APIRouter()
api_router = APIRouter()

router.include_router(dataset_router)
router.include_router(dataset_faqs_router)
router.include_router(datasets_property_router)
router.include_router(dataset_faqs_generation_router)
router.include_router(faqs_robot_router)
router.include_router(function_router)
router.include_router(agent_function_router)
router.include_router(ai_router)
router.include_router(robot_router)
router.include_router(dataset_robot_router)
router.include_router(questions_router)
router.include_router(attachments_router)
router.include_router(embeddings_router)
router.include_router(users_router)
router.include_router(stripes_router)
router.include_router(insider_preview_router)
router.include_router(report_router)
router.include_router(debug_router)
router.include_router(session_metrics_router)
router.include_router(digital_human_router)
# router.include_router(mock_router)
router.include_router(train_router)

router.include_router(plans_router)
router.include_router(add_ons_router)
router.include_router(user_quota_router)

router.include_router(plans_router)
router.include_router(add_ons_router)
router.include_router(user_quota_router)

# router for api
api_router.include_router(api_robot_router)

api_router.include_router(api_questions_router)
api_router.include_router(datasets_api_router)
api_router.include_router(api_digital_human_router)
